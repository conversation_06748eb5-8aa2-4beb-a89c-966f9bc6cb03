from pathlib import Path

from src.investigator.configuration.next_job_configuration import NextJobConfiguration
from src.investigator.log.monitoring import Monitoring


class NextJob(Monitoring):

    def __init__(self, workflow_process_id: int, relative_image_path: Path, next_job_config: NextJobConfiguration):
        super().__init__()
        self.workflow_process_id: int = workflow_process_id
        self.relative_image_path: Path = relative_image_path
        self.local_image_copy_created: bool = False
        self.next_job_config: NextJobConfiguration = next_job_config


    def get_workflow_process_id(self) -> int:
        return self.workflow_process_id


    def get_image_path(self, absolute: bool = False, working_local: bool = False) -> Path:
        """
        Get the image path. Per default the path retrieved from the ITF workflow is relative to the mount point,
        since network drives can be mounted differently (especially between windows and linux systems).

        :param absolute: If true, return the absolute image path (= mount point from the config + absolute image path)
        :param working_local: If true, return (absolute or relative) path for a local copy of the image (inside the tmp folder specified in the config)
        """
        if absolute:
            if working_local:
                return self.next_job_config.investigator_tmp_folder / self.relative_image_path
            else:
                return self.next_job_config.images_root_folder / self.relative_image_path
        else:
            return self.relative_image_path

    def get_working_image_path(self) -> Path:
        """
        Example: "//111.222.33.44/images/LKA-Börlin/AZ-1234565_Gunther_Gem/Gunther_ass_1/Gunther_ass_1.E01"
        """
        # return local image path if local copy has been created
        return self.get_image_path(absolute=True, working_local=self.local_image_copy_created)


    def get_xways_exe(self) -> Path:
        return self.next_job_config.xways_exe

    def get_xways_error_log_path(self) -> Path:
        return self.get_xways_exe().parent / "error.log"


    def get_xways_project_path(self, working_local: bool = False) -> Path:
        """
        Get the xways project path (path to .xfc file).
        Per default, the final path using the specified xways projects root folder is returned.

        :param working_local: If true, return a path in the local xways project folder (inside the tmp folder specified in the config)
        """
        # example relative_image_path = LKA-Bärlin/AZ-222_Gunther_Ber/Gunther_ass_1/Gunther_ass_1.E01
        case_folder_name = self.relative_image_path.parent.parent.name # AZ-222_Gunther_Ber
        evidence_item_folder_name = self.relative_image_path.parent.name # Gunther_ass_1

        if working_local:
            # e.g. D://rvs_station_tmp/xways_projects/Gunther_ass_1/Gunther_ass_1.xfc
            return (
                self.next_job_config.investigator_tmp_folder
                / "xways_projects"
                / evidence_item_folder_name
                / f"{evidence_item_folder_name}.xfc"
            )
        else:
            # e.g. //111.222.33.44/rvs_station/AZ-222_Gunther_Ber/Gunther_ass_1/Gunther_ass_1/Gunther_ass_1.xfc
            # note: we use evidence_item_folder_name twice because inside the top evidence_item_folder_name folder,
            # we are copying the xways project backup so it will be located next to the xways project with path replacement applied
            return (
                self.next_job_config.xways_projects_root_folder
                / case_folder_name
                / evidence_item_folder_name
                / evidence_item_folder_name
                / f"{evidence_item_folder_name}.xfc"
            )


    def set_local_image_copy_created(self, copy_created: bool):
        self.local_image_copy_created = copy_created

    def is_local_image_copy_created(self) -> bool:
        return self.local_image_copy_created


    def to_dict(self) -> dict[str, Path | int]:
        return {"workflow_process_id": self.get_workflow_process_id(),
                "relative_image_path": self.get_image_path(),
                "full_image_path": self.get_image_path(absolute=True),
                "working_image_path": self.get_working_image_path(),
                "xways_project_path": self.get_xways_project_path(),
                "working_xways_project_path": self.get_xways_project_path(working_local=True),
                "xways_exe": self.get_xways_exe()}