import shutil
import traceback
from pathlib import Path

from src.investigator.log.loglevels import LogLevel
from src.investigator.log.monitoring import Monitoring
from src.investigator.workflow.next_job import NextJob
from src.investigator.xways_file_handler import <PERSON><PERSON><PERSON>FileHandler
from src.investigator.xways_rvs.utils import remove_folder
from src.investigator.xways_rvs.xways_process_status import XwaysProcessStatus
from src.investigator.xways_rvs.xways_rvs_result import XwaysRVSResult


class XwaysRVSFollowUp(Monitoring):
    """
    Class for following up after a Refine Volume Snapshot (RVS, dt. "Datei-Überblick erweitern") action in X-WAYS.
    """

    def __init__(self, next_job: NextJob) -> None:
        super().__init__()
        self.next_job = next_job


    def follow_up_xways_rvs(self, xways_process_status: XwaysProcessStatus) -> XwaysRVSResult:
        """
        Determine if the RVS has been successful.

        If necessary, delete the local image copy and,
        if the Xways RVS was successful, then replace the image path in the XWays project file.

        Send process update FINISHED or FAILED to the workflow API.
        """
        xways_rvs_result = self.__determine_rvs_result(xways_process_status)
        self.log(LogLevel.INFO, f"The X-Ways RVS is considered \'{xways_rvs_result.value}\'.")

        # if failed, remove xways folder
        if xways_rvs_result == XwaysRVSResult.FAILED:
            remove_folder(self.next_job.get_xways_project_path(working_local=True).parent)
            self.log(LogLevel.INFO, "Deleted xways project because RVS failed.")
        else: # else, move xways project from tmp to final destination
            try:
                xways_project_folder = self.next_job.get_xways_project_path().parent
                # remove old folder if one exists (which can be because the RVS is maybe re-run)
                if xways_project_folder.exists():
                    remove_folder(xways_project_folder)

                shutil.move(
                    self.next_job.get_xways_project_path(working_local=True).parent,
                    xways_project_folder,
                )
                self.log(
                    LogLevel.INFO,
                    f"Moved X-Ways project folder from {self.next_job.get_xways_project_path(working_local=True)} to {self.next_job.get_xways_project_path().parent}.",
                )
            except Exception as _e:
                raise Exception(f"Failed to move xways project folder: {traceback.format_exc()}")


        # delete local image copy and replace image path
        if self.next_job.is_local_image_copy_created():
            image_copy_folder = self.next_job.get_working_image_path().parent.parent.parent
            self.__delete_folder(image_copy_folder)

            # replace image path in xways project file,
            # only if successful to use an unreplaced image path as hint for a failed RVS
            if xways_rvs_result == XwaysRVSResult.SUCCESSFUL:
                # replace the image path in the xways project, make a backup before modification
                XWaysFileHandler(
                    self.next_job.get_xways_project_path()
                ).replace_image_path(
                    self.next_job.get_image_path(absolute=True),
                    save_unmodified_xways_project_copy=True,
                )
                # TODO: find out why this is breaking the test image

        return xways_rvs_result


    def __determine_rvs_result(self, xways_process_status: XwaysProcessStatus) -> XwaysRVSResult:

        # check if xfc project has been created (ignore xways_process_status for that)
        if not self.next_job.get_xways_project_path(working_local=True).exists():
            self.log(LogLevel.INFO, "X-Ways did not create an .xfc file.")
            return XwaysRVSResult.FAILED

        # check if xways created an error.log file
        error_log_file = self.next_job.get_xways_error_log_path()
        if error_log_file.exists():
            self.log(LogLevel.WARNING, "X-Ways created an error.log file.")
            with open(error_log_file, "r") as f:
                error_log = f.read()
                self.log(LogLevel.INFO, f"X-Ways error.log:\n{error_log}.")

            return XwaysRVSResult.FAILED

        if xways_process_status == XwaysProcessStatus.FROZEN:
            return XwaysRVSResult.FROZEN

        # TODO: maybe check a few more indicators
        return XwaysRVSResult.SUCCESSFUL


    def __delete_folder(self, folder_path: Path) -> None:
        """ Delete a folder if it exists. """
        try:
            if folder_path.exists():
                shutil.rmtree(folder_path)
                self.log(LogLevel.INFO, f"Successfully deleted folder {folder_path}")
            else:
                self.log(LogLevel.WARNING, f"Folder to delete does not exists: {folder_path}")
        except Exception as _e:
            self.log(LogLevel.ERROR, f"Could not delete folder {folder_path}: {traceback.format_exc()}")
