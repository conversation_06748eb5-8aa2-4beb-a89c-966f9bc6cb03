from typing import cast
from pydantic import BaseModel
import cpuinfo
import psutil


class CPUInfo(BaseModel):
    brand: str
    speed_clock_hz: int
    count_cores_physical: int
    count_cores_logical: int

    @classmethod
    def measure(cls) -> "CPUInfo":
        info: dict[str, str | list[int]] = cpuinfo.get_cpu_info()
        return cls(
            brand=cast(str, info['brand_raw']),
            speed_clock_hz = cast(int, info['hz_actual'][0]),
            count_cores_physical = psutil.cpu_count(logical=False),
            count_cores_logical = psutil.cpu_count(logical=True)
        )