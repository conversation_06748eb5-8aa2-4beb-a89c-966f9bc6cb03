[project]
name = "investigator"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "click>=8.2.1",
    "concurrent-log-handler>=0.9.28",
    "criticalrequest",
    "numpy>=2.3.2",
    "psutil>=7.0.0",
    "py-cpuinfo>=9.0.0",
    "pydantic>=2.11.7",
    "types-psutil>=7.0.0.20250801",
]

[dependency-groups]
dev = [
    "pre-commit",
    "bandit",
    "ruff",
    "mypy",
    "pytest>=8.4.1",
    "pytest-httpserver>=1.1.3",
]

[tool.uv.sources]
criticalrequest = { git = "ssh://************************:222/IT-Forensik/CriticalRequest" }

[tool.bandit]
skips = ["B404 "] # This suppresses B404 warnings: Import of subprocess
exclude_dirs = ["tests"]

[tool.ruff]
# Assume Python 3.12
target-version = "py312"

[tool.ruff.lint]
select = ["ALL"]  # Maximum strictness globally
ignore = ["D100", "D101", "D102", "D103", "D104", "D105", "D107", "S104", "TD003", "FIX002", "BLE001", "C901"]  # ignored documentation and SOME SECURITY rules

[tool.ruff.lint.extend-per-file-ignores]
"tests/**/*.py" = ["PLR2004", "S101", "E501"]  # Ignore "magic numbers" and "assert usage in tests"

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = false

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"

[tool.pytest.ini_options]
pythonpath = [
  "src"
]

[tool.mypy]
files = ["src"]
strict = true

[tool.mypy-pySMART]
ignore_missing_imports = true

[tool.mypy-pyudev]
ignore_missing_imports = true

[tool.mypy-psutil]
ignore_missing_imports = true
