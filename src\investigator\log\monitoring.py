import logging
from logging import Logger
from src.investigator.log.loglevels import LogLevel


class Monitoring:
    def __init__(self, logger: Logger | None = None) -> None:
        if logger is None:
            logger = logging.getLogger(self.__class__.__name__)
        self.logger: Logger = logger

    def set_logger(self, logger: Logger) -> None:
        self.logger = logger

    def log(self, level: LogLevel, message: str) -> None:
        if self.logger is not None:
            self.logger.log(level, message)
