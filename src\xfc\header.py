from .blocks import (
    BasicBlock,
    StrBlock,
    MagicBlock,
    IntNumberBlock,
    EmbeddedBlock,
)


class XFCHeader(BasicBlock):
    def __init__(self) -> None:
        header = StrBlock("utf-8", size=96)
        version = StrBlock("utf-8", size=64)
        user = StrBlock("utf-16le", size=64)
        user_generic = StrBlock("utf-16le", size=84)
        size_file_name = IntNumberBlock(4)
        file_name = StrBlock("utf-16le", 16)

        super().__init__(sub_blocks=[
            header,
            BasicBlock(size=80),
            version,
            BasicBlock(size=16),
            user,
            BasicBlock(size=878),
            MagicBlock(bytes([0x00, 0x01, 0x00, 0x00,])),
            user_generic,
            MagicBlock(bytes([0x03, 0x00,])),
            size_file_name,
            EmbeddedBlock(
                sub_blocks=[file_name], 
                end_block=MagicBlock(bytes([
                    0x0B, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
                ]))),
        ])
        self.header = header
        self.version = version
        self.user = user
        self.user_generic = user_generic
        self.file_name = file_name