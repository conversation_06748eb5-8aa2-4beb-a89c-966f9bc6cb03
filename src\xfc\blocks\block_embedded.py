from .block_basic import BasicBlock, IBlock
from .index import Index


class EmbeddedBlock(BasicBlock):
    def __init__(
            self, 
            sub_blocks: list[IBlock], 
            start_block: IBlock|None = None, 
            end_block: IBlock|None = None,
            size_include_start: bool = True,
            size_include_end: bool = True,
        ):
        super().__init__(sub_blocks=sub_blocks)
        self.start_block = start_block
        self.end_block = end_block
        self.embedded_blocks = sub_blocks
        self.size_include_start = size_include_start
        self.size_include_end = size_include_end

    def process(self, data, start_at: Index = Index(0), end_at: Index = Index(-1)):
        if self.start_block is not None:
            self.start_block.process(data, start_at)
            if self.size_include_start:
                self.set_start(self.start_block.get_start())
            else:
                self.set_start(self.start_block.get_end())
            start = self.start_block.get_end()
        else:
            self.set_start(start_at)
            start = start_at

        if self.end_block is not None:
            self.end_block.process(data, self.get_start())
            if self.size_include_end:
                self.set_size(self.end_block.get_end() - self.get_start())
            else:
                self.set_size(self.end_block.get_start() - self.get_start())
            end = self.end_block.get_start()
        else:
            self.set_size(end_at - self.get_start())
            end = self.get_end()
        self._data = data
        for block in self.embedded_blocks:
            block.process(data, start, end)
            start = block.get_end()
        return None