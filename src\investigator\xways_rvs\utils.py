import errno
import logging
import os
import shutil
import stat
from pathlib import Path

import psutil


def format_size(bytes_size) -> str:
    """
    Convert bytes into a human-readable format (KB, MB, GB, etc.).
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_size < 1024:
            return f"{bytes_size:.2f} {unit}"
        bytes_size /= 1024
    return f"{bytes_size:.2f} PB"


def is_programm_running(process_name: str) -> bool:
    for proc in psutil.process_iter(["name", "cmdline"]):
        try:
            if process_name in proc.info["name"]:
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return False


def kill_processes_by_name(process_name: str) -> int:
    """Kill processes with the given name and returns the number of killed processes.

    Args:
        process_name (str): name of the process to be killed.

    Returns:
        int: number of killed processes
    """
    killed_processes = 0
    for proc in psutil.process_iter(['name']):
        try:
            if proc.info['name'] == process_name:
                logging.info(f"Killing process {proc.pid} ({proc.info['name']})")
                proc.terminate()  # Sends SIGTERM or equivalent
                killed_processes += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    return killed_processes


def remove_folder(folder_path: Path) -> None:
    def handle_remove_readonly(func, path, exc) -> None:
        """
        If the folder or file to be removed is read-only for some reason (which it strangely is at
        the internal drive D:/ of the InvestiGator server), then set permissions to 777 and
        try removing it again.
        """
        exc_value = exc[1]
        if func in (os.rmdir, os.remove) and exc_value.errno == errno.EACCES:
            os.chmod(path, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)  # 0777
            func(path)
        else:
            msg = ""
            raise RuntimeError(msg)

    shutil.rmtree(folder_path, ignore_errors=False, onerror=handle_remove_readonly)
