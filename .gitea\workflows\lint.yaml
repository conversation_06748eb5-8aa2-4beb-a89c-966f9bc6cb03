name: RuffLinting

on:
  push:
    branches: [main, master]

jobs:
  run-ruff:
    name: <PERSON><PERSON>
    runs-on: debian-12
    steps:
      - name: SSH-key setup
        env:
          SSH_PRIVATE_KEY: ${{ secrets.ACTIONS_BASE64 }}
        run: |
          mkdir -p ~/.ssh
          echo $SSH_PRIVATE_KEY | base64 -w0 --decode > ~/.ssh/id_ed25519
          ssh-keyscan -p 222 gitea.digifors.local > ~/.ssh/known_hosts
          chmod 600 ~/.ssh/id_ed25519

      - name: Git port setup
        run: |
          mkdir -p ~/.ssh
          echo 'Host gitea.digifors.local' >> ~/.ssh/config
          echo '  HostName gitea.digifors.local' >> ~/.ssh/config
          echo '  IdentityFile ~/.ssh/id_ed25519' >> ~/.ssh/config
          echo '  Port 222' >> ~/.ssh/config
          
      - name: Git checkout
        run: |
          git clone "ssh://************************:222/${{ gitea.repository }}.git" ${{gitea.workspace}}
          git fetch origin ${{ gitea.ref }}:pr
          git checkout pr

      - name: Install uv
        run: curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Install the project
        run: uv sync --locked --all-extras --dev
        
      - name: Run formatting check with ruff
        run: uv run ruff check