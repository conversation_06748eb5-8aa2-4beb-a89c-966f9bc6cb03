import json
import traceback
from pathlib import Path

from criticalrequest import crit_request, RequestMethod

from src.investigator.configuration.workflow_configuration import WorkflowConfiguration
from src.investigator.configuration.next_job_configuration import NextJobConfiguration
from src.investigator.log.loglevels import LogLevel
from src.investigator.log.monitoring import Monitoring
from src.investigator.workflow.next_job import NextJob


class NextJobRequester(Monitoring):

    def __init__(self, workflow_config: WorkflowConfiguration, next_job_config: NextJobConfiguration) -> None:
        super().__init__()
        self.investigator_config = workflow_config
        self.next_job_config = next_job_config


    def reserve_next_job(self) -> NextJob | None:
        """
        Fetch the next job in the queue from the workflow API and reserves it.
        """
        url = f"{self.investigator_config.api_url}/forensic-steps/{self.investigator_config.investigator_step_id}/next-job"
        try:

            self.log(LogLevel.INFO, f"Fetching next job from {url}")
            run = True
            while run:
                try:
                    response = crit_request(
                        RequestMethod.GET,
                        url,
                        timeout=30.0,
                    )
                    run = False
                except TimeoutError:
                    run = True
                    self.log(LogLevel.WARNING, "Timeout while fetching next job. Retrying ...")

            if response.status_code == 404:
                self.log(LogLevel.INFO, "No next job (response code 404).")
                return None

            response.raise_for_status()
            response_json = response.json()
            self.log(LogLevel.INFO, f"Received next job: {response_json}")

            # extract next job data from response
            workflow_process_id = self.__get_workflow_process_id(response_json)
            relative_image_path = self.__get_first_image_file_path(response_json)

            if workflow_process_id is not None and relative_image_path is not None:
                return NextJob(workflow_process_id, relative_image_path, self.next_job_config)

        except Exception as _e:
            self.log(LogLevel.ERROR,f"Error fetching next job: {traceback.format_exc()}")

        return None

    def __get_workflow_process_id(self, workflow_process_json: dict) -> int | None:
        if "id" in workflow_process_json:
            try:
                return int(workflow_process_json["id"])
            except Exception as _e:
                self.log(LogLevel.ERROR,
                         f"Error getting 'id' from workflow process as integer: {traceback.format_exc()}")

        return None

    def __get_first_image_file_path(self, workflow_process_json: dict) -> Path | None:
        if not all(key in workflow_process_json for key in ["id", "input_data"]):
            self.log(LogLevel.ERROR, "Next job does not contain key 'id' or 'input_data'.")
            return None

        # parse input_data
        try:
            input_data: dict = json.loads(workflow_process_json["input_data"])
        except json.JSONDecodeError as _e:
            self.log(LogLevel.ERROR, f"Could not parse input_data: {traceback.format_exc()}")
            return None

        if "first_image_file_path" not in input_data:
            self.log(LogLevel.ERROR, "Next job's input_data does not contain key 'first_image_file_path'.")
            return None

        return Path(input_data["first_image_file_path"])







