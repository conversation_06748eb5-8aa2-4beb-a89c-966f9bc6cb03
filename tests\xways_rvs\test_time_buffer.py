from datetime import UTC, datetime, timedelta

import pytest

from src.investigator.xways_rvs.time_buffer import ExpiringValueStore


def test_append_and_retrieve_values(monkeypatch: pytest.MonkeyPatch) -> None:
    store = ExpiringValueStore(max_age_minutes=10)
    fixed_time = datetime(2023, 1, 1, 12, 0, tzinfo=UTC)

    monkeypatch.setattr("datetime.datetime", lambda: fixed_time)
    store.append(5.0)
    store.append(10.0)

    values = store.get_values()
    assert values == [5.0, 10.0]

def test_expiry_behavior(monkeypatch: pytest.MonkeyPatch) -> None:
    store = ExpiringValueStore(max_age_minutes=5)
    base_time = datetime(2023, 1, 1, 12, 0, tzinfo=UTC)

    monkeypatch.setattr("datetime.datetime", lambda: base_time)
    store.buffer.append((base_time - timedelta(minutes=6), 1.0))
    store.buffer.append((base_time - timedelta(minutes=4), 2.0))

    store._remove_old(base_time)  # noqa: SLF001
    assert store.get_values() == [2.0]

def test_get_oldest(monkeypatch: pytest.MonkeyPatch) -> None:
    store = ExpiringValueStore(max_age_minutes=10)
    now = datetime(2023, 1, 1, 12, 0, tzinfo=UTC)

    monkeypatch.setattr("datetime.datetime", lambda: now)
    store.buffer.append((now, 100.0))
    store.buffer.append((now + timedelta(minutes=1), 200.0))

    oldest = store.get_oldest()
    assert oldest == (now, 100.0)

def test_get_oldest_empty() -> None:
    store = ExpiringValueStore(max_age_minutes=10)
    assert store.get_oldest() is None
