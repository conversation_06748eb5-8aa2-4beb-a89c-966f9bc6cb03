import os
import logging.config
from pathlib import Path

LOG_FILE_PATH = str((Path(__file__).parent.parent.parent.parent.resolve() / "investigator_py.log").absolute())
print(f"Log file: {LOG_FILE_PATH}")

def setup_logging():
    # # needed when running python script from OpenRPA to avoid problems with certain file paths
    # sys.stdout.reconfigure(encoding='utf-8')

    # Creates file if not exist
    if not os.path.exists(LOG_FILE_PATH):
        with open(LOG_FILE_PATH, 'w') as fp:
            pass

    # Configure logging
    LOGGING_CONFIG = {
        "version": 1,
        "root": {
            "handlers": ["rotatingFileHandler"],
            "level": logging.INFO
        },
        "handlers": {
            # rotate log file if it is bigger than 5 MB (roll content over to file 'investigator_py.log.n')
            "rotatingFileHandler": {
                "formatter": "std_out",
                "class": "concurrent_log_handler.ConcurrentRotatingFileHandler",
                "level": logging.INFO,
                "filename": LOG_FILE_PATH,
                "mode": "a",
                "maxBytes": 5*1024*1024,
                "backupCount": 5,
                "encoding": 'utf-8'
            }
        },
        "formatters": {
            "std_out": {
                "format": "[%(asctime)s] %(levelname)s %(name)s - %(message)s",
                "datefmt": '%Y-%m-%d@%H:%M:%S(%z)'
            }
        },
    }
    logging.config.dictConfig(LOGGING_CONFIG)
