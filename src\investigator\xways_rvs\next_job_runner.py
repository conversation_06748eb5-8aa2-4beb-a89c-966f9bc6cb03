import time

from src.investigator.log.loglevels import LogLevel
from src.investigator.workflow.investigator_status import InvestigatorStatus
from src.investigator.xways_rvs.xways_rvs_result import XwaysRVSResult

from .xways_rvs_preparation import XwaysRVSPreparation
from .xways_rvs_process import XwaysRVSProcess
from .xways_process_status import XwaysProcessStatus
from .xways_rvs_follow_up import XwaysRVSFollowUp
from src.investigator.configuration.configuration import Configuration
from src.investigator.log.monitoring import Monitoring
from src.investigator.workflow.next_job import NextJob
from src.investigator.workflow.workflow_process_updater import WorkflowProcessUpdater
from src.investigator.benchmark import CPUInfo, RAMInfo, BenchmarkData, ProcessInfo



class NextJobRunner(Monitoring):
    def __init__(self, config: Configuration) -> None:
        super().__init__()
        self.config = config
        self.cpu = CPUInfo.measure()
        self.ram = RAMInfo.measure()

    def run(self, next_job: NextJob) -> bool:
        workflow_process_updater = WorkflowProcessUpdater(self.config.workflow)
        workflow_process_updater.update_investigator_state(
            next_job.get_workflow_process_id(),
            InvestigatorStatus.DOWNLOADING,
        )
        preparation = XwaysRVSPreparation(next_job, self.config.next_job)
        start = time.time()
        is_success = preparation.prepare_for_xways_rvs(workflow_process_updater)
        duration_download = time.time() - start
        
        if not is_success:
            self.log(LogLevel.ERROR, f"Failed to prepare job: {next_job.to_dict()}")
            workflow_process_updater.fail_workflow_process(
                next_job.get_workflow_process_id(),
            )
            return False

        workflow_process_updater.update_investigator_state(
            next_job.get_workflow_process_id(),
            InvestigatorStatus.RVS
        )
        xways_rvs_process = XwaysRVSProcess(next_job)
        start = time.time()
        xways_rvs_process.start()
        while True:
            xways_process_result, resource_usage = xways_rvs_process.check_status()
            if xways_process_result != XwaysProcessStatus.RUNNING:
                break
            workflow_process_updater.update_investigator_state(
                next_job.get_workflow_process_id(),
                InvestigatorStatus.RVS
            )
            time.sleep(20)
        duration_rvs = time.time() - start
        # run postprocessing of xways rvs
        workflow_process_updater.update_investigator_state(
            next_job.get_workflow_process_id(),
            InvestigatorStatus.UPLOADING
        )
        start = time.time()
        result = XwaysRVSFollowUp(next_job).follow_up_xways_rvs(xways_process_result)
        duration_upload = time.time() - start

        
        if result == XwaysRVSResult.SUCCESSFUL:
            benchmark = BenchmarkData(
                process=ProcessInfo(
                    size_image_bytes=preparation.get_image_folder_size(),
                    time_dowload_s=duration_download,
                    time_rvs_s=duration_rvs,
                    time_upload_s=duration_upload,
                    xways_usage=resource_usage,
                ),
                cpu=self.cpu,
                ram=self.ram,
            )
            workflow_process_updater.successfully_end_workflow_process(
                next_job.get_workflow_process_id(),
                xways_project_path=next_job.get_xways_project_path(),
                benchmark=benchmark,
            )
        else:
            workflow_process_updater.fail_workflow_process(
                next_job.get_workflow_process_id(),
            )
        self.log(LogLevel.INFO, f"Finished running next job.")
        return is_success