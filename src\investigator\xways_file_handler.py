import shutil

from src.investigator.log.loglevels import LogLevel
from src.investigator.log.monitoring import Monitoring
from src.xfc import XFC
from pathlib import Path

class XWaysFileHandler(Monitoring):
    def __init__(self, xfc_path: Path) -> None:
        super().__init__()
        if not xfc_path.is_file():
            msg = "File not found!"
            raise ValueError(msg)
        if xfc_path.suffix != ".xfc":
            msg = "Expected file with .xfc suffix!"
            raise ValueError(msg)
        self.xfc_path = xfc_path

    def replace_image_path(self,
                           new_image_path: Path,
                           known_old_image_path: Path | None = None,
                           new_xfc_path: Path | None = None,
                           update_backups: bool = True,
                           save_unmodified_xways_project_copy: bool = False
                           ) -> None:
        if save_unmodified_xways_project_copy:
            self.__save_copy_of_unmodified_xways_project(new_image_path)

        xfc = XFC()
        paths = [self.xfc_path]
        if update_backups:
            for item in self.xfc_path.parent.iterdir():
                if not item.is_file():
                    continue
                if item.stem == self.xfc_path.name:
                    paths.append(item)
        for path in paths:
            xfc.load_from_file(path)
            if known_old_image_path is not None:
                if known_old_image_path != xfc.path.get_path():
                    msg = f"Found: {xfc.path.get_path()!s} (expected: {known_old_image_path!s})"
                    raise ValueError(msg)
            replacements = xfc.path.change_path(new_image_path)
            if len(replacements) == 0:
                self.log(LogLevel.ERROR, f"Was not able to identify changes in .xfc file: {xfc.path!s}")
            xfc_new = XFC(xfc.generate_new_data(replacements))
            if new_xfc_path is not None:
                sufixes = "".join(path.suffixes)
                xfc_new.save_data(new_xfc_path.parent / f"{new_xfc_path.stem}{sufixes}")
                continue
            xfc_new.save_data(self.xfc_path)

    def __save_copy_of_unmodified_xways_project(self, new_image_path: Path):
        # save a backup of the original xways project before modification
        xways_project_folder_copy = Path(f"{str(self.xfc_path.parent)}_backup_original")
        if xways_project_folder_copy.exists():
            shutil.rmtree(xways_project_folder_copy)
        shutil.copytree(self.xfc_path.parent, xways_project_folder_copy)
        # provide the image path for manual replacement in a txt file
        with open(xways_project_folder_copy / "original_image_path.txt", "w") as file:
            file.write(str(new_image_path))

    
