from __future__ import annotations
from .i_marker import IMarker
from dataclasses import dataclass
from .marker import Marker


@dataclass(frozen=True)
class MergeWidth:
    idx: int
    length: int


class BlockReplacer:
    def __init__(self, original: IMarker, new_data: bytes) -> None:
        self.original = original
        self.new_data = new_data

    def get_size_diff(self)-> int:
        """_summary_

        Returns:
            int: does the new size is larger (> 0), or smaller (< 0)
        """
        return len(self.new_data) - self.original.get_size()

    def replace(self, data: bytes) -> bytes:
        return data[:self.original.get_start()] + self.new_data + data[self.original.get_end():]

    @staticmethod
    def simplify(replacers: list[BlockReplacer]) -> list[BlockReplacer]:
        replacers.sort(key=lambda r: r.original.get_start())



        mergerable = list[MergeWidth]()
        i: int = 0
        while i < len(replacers):
            length = 1
            j: int = i + length
            while j < len(replacers):
                r_current: BlockReplacer = replacers[j-1]
                r_next: BlockReplacer = replacers[j]
                is_adjacent = (
                    r_current.original.get_end() + 1 == r_next.original.get_start()
                )
                if is_adjacent:
                    length = length +1
                else:
                    break
            mergerable.append(MergeWidth(i, length))
            i = i + length

        result = list[BlockReplacer]()
        for merger in mergerable:
            data = bytes()
            for i in range(merger.length):
                idx = merger.idx + i
                data += replacers[idx].new_data
            start = replacers[idx].original.get_start()
            end = replacers[idx + merger.length -1].original.get_end()
            marker = Marker(start, end-start)
            result.append(BlockReplacer(marker, data))
        return result
