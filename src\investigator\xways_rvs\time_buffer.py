from collections import deque
from datetime import datetime, timedelta

class ExpiringValueStore:
    def __init__(self, max_age_minutes) -> None:
        self.max_age = timedelta(minutes=max_age_minutes)
        self.buffer: deque[tuple[datetime, float]] = deque()

    def append(self, value: float) -> None:
        now = datetime.now()
        self.buffer.append((now, value))
        self._remove_old(now)

    def _remove_old(self, current_time: datetime) -> None:
        while self.buffer and current_time - self.buffer[0][0] > self.max_age:
            self.buffer.popleft()

    def get_values(self) -> list[float]:
        return [val for _ts, val in self.buffer]
    
    def get_oldest(self) -> tuple[datetime, float] | None:
        if self.buffer:
            return self.buffer[0]
        return None
