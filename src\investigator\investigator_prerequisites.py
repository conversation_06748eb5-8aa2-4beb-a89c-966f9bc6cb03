import traceback
from pathlib import Path

import requests

from src.investigator.configuration.next_job_configuration import NextJobConfiguration
from src.investigator.configuration.workflow_configuration import WorkflowConfiguration
from src.investigator.log.loglevels import LogLevel
from src.investigator.log.monitoring import Monitoring
from src.investigator.prerequisites_check_status import PrerequisitesCheckStatus


class InvestiGatorPrerequisiteCheck(Monitoring):

    def __init__(self, workflow_config: WorkflowConfiguration, next_job_config: NextJobConfiguration):
        super().__init__()
        self.workflow_config = workflow_config
        self.next_job_config = next_job_config


    def are_all_prerequisites_fulfilled(self) -> bool:
        try:
            prerequisites_check_results = self.run_check()
            check_result_sum = sum(prerequisites_check_results)
            self.log(LogLevel.INFO, f"Sum of prerequisites check results: {check_result_sum}")
            return check_result_sum == sum(list(PrerequisitesCheckStatus))

        except Exception as exception:
            self.log(LogLevel.ERROR, f"Failed to check prerequisites: {traceback.format_exc()}")
            return False


    def run_check(self) -> list[PrerequisitesCheckStatus]:
        check_results: list[PrerequisitesCheckStatus] = []

        if self.__is_workflow_api_reachable():
            check_results.append(PrerequisitesCheckStatus.WORKFLOW_API_REACHABLE)

        if self.__path_exists(self.next_job_config.xways_projects_root_folder):
            check_results.append(PrerequisitesCheckStatus.XWAYS_PROJECT_ROOT_FOLDER_EXISTS)

        if self.__path_exists(self.next_job_config.images_root_folder):
            check_results.append(PrerequisitesCheckStatus.IMAGES_ROOT_FOLDER_EXISTS)

        if self.__path_exists(self.next_job_config.xways_exe):
            check_results.append(PrerequisitesCheckStatus.XWAYS_EXE_EXISTS)

        return check_results


    def __is_workflow_api_reachable(self) -> bool:
        try:
            response = requests.get(self.workflow_config.api_url, timeout=10)

            if response.status_code:
                return True
            else:
                self.log(LogLevel.ERROR, f"Workflow API not reachable. No status code received.")
                return False
        except Exception as exception:
            self.log(LogLevel.ERROR, f"Reaching workflow API failed: {traceback.format_exc()}")
            return False

    def __path_exists(self, p: Path) -> bool:
        try:
            return p.exists()
        except Exception as exception:
            self.log(LogLevel.ERROR, f"Failed to check path {str(p)}: {traceback.format_exc()}")
            return False
