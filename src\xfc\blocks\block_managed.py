from .block_number import IntNumber<PERSON>lock
from .block_basic import BasicBlock, IBlock
from .index import Index, ChildIndex
from .replacer import BlockReplacer
from .block_buffer import ZeroBufferBlock


class DynamicSizedBlock(BasicBlock):
    def __init__(self, size_block: IntNumberBlock, changing_blocks: list[IBlock], buffer: ZeroBufferBlock | None = None):
        sub_blocks=changing_blocks
        if buffer is not None:
            sub_blocks.append(buffer)
        super().__init__(sub_blocks=sub_blocks)
        self.size_block = size_block
        self.buffer = buffer

    def process(self, data, start_at = Index(0), end_at = Index(-1)):
        super().process(data, start_at, end_at)

    def coordinate_replacement(
            self, 
            child_index: ChildIndex, 
            replacers: list[BlockReplacer],
        ) -> list[BlockReplacer]:

        def process_replacers(_replacers: list[BlockReplacer]) -> list[BlockReplacer]:
            info = self.get_parent_info()
            if info is not None:
                return BasicBlock.coordinate_replacement(self, info.child_index, _replacers)
            else:
                return _replacers
            
        if isinstance(self.get_children()[child_index],ZeroBufferBlock):
            return process_replacers(replacers)
        change = 0
        for replacer in replacers:
            change += replacer.get_size_diff()
        if change == 0:
            return process_replacers(replacers)
        if self.buffer is not None and self.buffer.get_size() > change:
            replacers += self.buffer.change_size(-1*change)
        else:
            replacers += self.size_block.change_value(self.size_block.get_value() + change -1)
        return process_replacers(BlockReplacer.simplify(replacers))
