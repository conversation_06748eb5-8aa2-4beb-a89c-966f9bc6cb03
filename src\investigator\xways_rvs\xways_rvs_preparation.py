import shutil
import threading
import time
import traceback
import hashlib
from pathlib import Path

from src.investigator.configuration.next_job_configuration import NextJobConfiguration
from src.investigator.log.loglevels import LogLevel
from src.investigator.log.monitoring import Monitoring
from src.investigator.workflow.investigator_status import Investigator<PERSON>tatus
from src.investigator.workflow.next_job import <PERSON><PERSON><PERSON>
from src.investigator.workflow.workflow_process_updater import WorkflowProcessUpdater
from src.investigator.xways_rvs.utils import format_size, remove_folder


class XwaysRVSPreparation(Monitoring):
    """Class for preparing running Refine Volume Snapshot.

    RVS (dt. "Datei-Überblick erweitern") action in X-WAYS.
    """

    def __init__(self, next_job: NextJob, job_config: NextJobConfiguration) -> None:
        super().__init__()
        self.next_job_config = job_config
        self.next_job = next_job


    def prepare_for_xways_rvs(self, workflow_updater: WorkflowProcessUpdater) -> bool:
        """Prepares the folder structure for the XWAYS RVS process.

        This includes:
        - Send process RUNNING to workflow.
        - Create X-WAYS project folder and copy image to local if possible.
        - Send process FAILED if any problems occur.
        - Return True if no problems occurred.
        """
        self.log(LogLevel.INFO, f"Preparing X-Ways RVS for job: \n {self.next_job.to_dict()}")

        # delete error logging file in xways installation if exists from previous RVS
        prev_err_log = self.next_job.get_xways_error_log_path()
        prev_err_log.unlink(missing_ok=True)

        # sync WinHex.cfg from installation folder to X-Ways executable folder
        source_cfg = Path(__file__).parent.parent.parent.parent / "installation" / "WinHex.cfg"
        dest_cfg = self.next_job.get_xways_exe().parent / "WinHex.cfg"
        shutil.copy(source_cfg, dest_cfg)
        self.log(LogLevel.INFO, f"Copied WinHex.cfg to {dest_cfg}")

        # check that image really exists
        if not self.next_job.get_image_path(absolute=True).exists():
            self.log(
                LogLevel.ERROR,
                f"Image does not exist: {self.next_job.get_image_path(absolute=True)!s}",
            )
            return False
        try:
            self.next_job.get_xways_project_path(
                working_local=True,
            ).parent.mkdir(parents=True, exist_ok=True)
            self.log(LogLevel.INFO, f"Created folder {self.next_job.get_xways_project_path(working_local=True).parent}.")
        except OSError:
            self.log(
                LogLevel.ERROR,
                f"Could not create local xways project folder: {traceback.format_exc()}",
            )
            return False

        # test if has sufficient storage space
        image_folder_size = self.get_image_folder_size()
        _total, _used, local_free_space = shutil.disk_usage(
            self.next_job_config.investigator_tmp_folder,
        )
        self.log(
            LogLevel.INFO,
            f"Image folder size: {format_size(image_folder_size)}, local space available: {format_size(local_free_space)}")

        if image_folder_size > local_free_space:
            self.log(
                LogLevel.WARNING,
                f"Not enough space on disk to copy {self.next_job.get_image_path(absolute=True)}. "
                "Using original image path.",
            )
            return True

        # copy files locally
        thread = self.copy_image_to_local()
        if thread is not None:
            while thread.is_alive():
                workflow_updater.update_investigator_state(
                    self.next_job.get_workflow_process_id(),
                    InvestigatorStatus.DOWNLOADING,
                )
                time.sleep(20)
            thread.join()

        # Check if the image copy was actually successful
        copy_was_successful = True
        if not self.next_job.local_image_copy_created:
            copy_was_successful = False
            self.log(
                LogLevel.ERROR,
                "Image copy verification failed or was incomplete.",
            )

        if not copy_was_successful:
            local_image_folder = self.next_job.get_image_path(
                absolute=True,
                working_local=True,
            ).parent
            self.log(
                LogLevel.ERROR,
                f"Failed to create a local copy of {self.next_job.get_image_path(absolute=True)} at {local_image_folder}: {traceback.format_exc()}",
                )

            # cleanup
            if local_image_folder.exists():
                self.log(
                    LogLevel.INFO,
                    "Deleting files from unfinished copying process",
                )
                remove_folder(local_image_folder)
                self.next_job.set_local_image_copy_created(False)
                self.log(
                    LogLevel.WARNING,
                    "Image not marked as local copied after copying process",
                )
            return False

        self.log(
            LogLevel.INFO,
            "Successfully copied image and verified it.",
        )
        return True


    def copy_image_to_local(self) -> threading.Thread:
        """Copy the image folder to local."""
        image_copy_file = self.next_job.get_image_path(absolute=True, working_local=True)
        image_copy_folder = image_copy_file.parent
        self.log(LogLevel.INFO, f"Start copying image to {image_copy_folder} ...")

        # to be safe, delete any previous copy if exist for any reason
        if image_copy_folder.exists():
            remove_folder(image_copy_folder)

        # parallel copy the folder structure
        def worker() -> None:
            try:
                src = Path(self.next_job.get_image_path(absolute=True)).parent
                dst = Path(image_copy_folder)
                shutil.copytree(src, dst)
                self.log(LogLevel.INFO, "Copying image successful.")
            except Exception:
                self.log(LogLevel.ERROR, f"Exception during image copy: {traceback.format_exc()}")
                self.next_job.set_local_image_copy_created(False)
                return

            def file_info(path: Path) -> dict:
                return {
                    str(f.relative_to(path)): f.stat().st_size
                    for f in path.rglob("*") if f.is_file()
                }

            if file_info(src) == file_info(dst):
                self.log(LogLevel.INFO, "Image copy verified successfully.")
                self.next_job.set_local_image_copy_created(True)
            else:
                self.log(LogLevel.ERROR, "Image copy failed: file count or size mismatch.")
                self.next_job.set_local_image_copy_created(False)

        t = threading.Thread(target=worker)
        t.start()
        return t

    def get_image_folder_size(self) -> int:
        """Calculate the total size of the image folder.

        This Funkction also includes all subdirectories and files.
        """
        root_directory = self.next_job.get_image_path(absolute=True).parent
        return sum(f.stat().st_size for f in root_directory.glob("**/*") if f.is_file())
