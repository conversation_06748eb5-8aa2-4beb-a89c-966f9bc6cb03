import time
from pathlib import Path
import subprocess
import traceback
import psutil
from datetime import datetime, timedelta

from src.investigator.benchmark.info_resource_usage import ResourceUsage, get_process_by_name
from src.investigator.log.loglevels import LogLevel
from src.investigator.log.monitoring import Monitoring
from src.investigator.workflow.next_job import NextJob
from src.investigator.xways_rvs.time_buffer import ExpiringValueStore
from src.investigator.xways_rvs.utils import is_programm_running, kill_processes_by_name
from src.investigator.xways_rvs.xways_process_status import XwaysProcessStatus


class XwaysRVSProcess(Monitoring):
    """
    Class for running an Refine Volume Snapshot (RVS, dt. "Datei-Überblick erweitern") action in X-WAYS.
    """

    def __init__(self, next_job: NextJob) -> None:
        super().__init__()
        self.next_job = next_job
        self.cpu_history = ExpiringValueStore(5)
        self.process_name = "xwb64.exe"
        self.xways_process: subprocess.Popen[str] | None = None
        self.ressource_usage: ResourceUsage | None = None
    

    def start(self) -> None:
        # kills running xways instance before to ensure we are not asked for "open another instance or resume the other one" in a popup dialog
        xways_already_running = is_programm_running(self.process_name)
        if xways_already_running:
            self.log(LogLevel.WARNING, "Detected already running xways instances.")
            # Ensure there is no xways instance already running. This can happen,
            # if the security option "Nach Absturz neustarten" is enabled (which is not recommended for this automation).
            killed_processes = kill_processes_by_name(self.process_name)
            self.log(LogLevel.INFO, f"Killed {killed_processes} xways processes.")

        
        cmd: list[Path | str] = [
            self.next_job.get_xways_exe(),
            f"NewCase:{str(self.next_job.get_xways_project_path(working_local=True))!s}",
            f"AddImage:{str(self.next_job.get_working_image_path())!s}",
            f"Override:0", # don't simulate "ok" or "cancel" when unexpected popup pop up. In this way X-Ways stays open and the process correctly fails due to CPU inactivity over time.
            "RVS:~",
            "auto"
        ]
        self.log(LogLevel.INFO, f"Running command: {' '.join(map(str, cmd))}")

        self.xways_process = subprocess.Popen(  # noqa: S603
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        self.ressource_usage = ResourceUsage()

    def check_status(self) -> tuple[XwaysProcessStatus, ResourceUsage]:
        if self.ressource_usage is None:
            raise RuntimeError("ressource_usage of this xways_rvs_process is None.")

        status = self.get_status()
        if status == XwaysProcessStatus.RUNNING:
            self.log(LogLevel.INFO, "X-WAYS is still running...")
            xways_psutil_process = get_process_by_name(self.process_name)
            if xways_psutil_process is None:
                raise RuntimeError(f"Could not find process {self.process_name}.")
            self.ressource_usage.update(xways_psutil_process)
            return XwaysProcessStatus.RUNNING, self.ressource_usage
        if status == XwaysProcessStatus.FROZEN:
            if self.xways_process is not None:
                self.xways_process.kill()
                # give xways time for deleting temporary files in the
                # project to avoid file-not-found when copying the folder later
                time.sleep(60)
                # if x-ways is still frozen -> burn it with fire!
                if self.xways_process.poll() is not None:
                    self.xways_process.terminate()
            return XwaysProcessStatus.FROZEN, self.ressource_usage
        exit_code = self.get_exit_code()
        self.log(LogLevel.INFO, f"X-Ways ended with exit code {exit_code}")
        # note: exit code seems to be 710 when terminated
        # note: If an exception had been raised that X-Ways handled itself, then the exit code will be 710 as well.
        #   Exception (criticality level depends on a setting in X-Ways) will be found in the file error.log in the x-ways installation folder.
        if exit_code is None or exit_code != 710:
            return XwaysProcessStatus.FAILED, self.ressource_usage
        return XwaysProcessStatus.FINISHED, self.ressource_usage

    def get_status(self) -> XwaysProcessStatus:
        # process is running as long as no return code is provided
        if self.xways_process is None:
            msg = "self.xways_process is None, which likely means that start() has not been called before."
            raise RuntimeError(msg)
        if self.xways_process.poll() is not None:
            return XwaysProcessStatus.FINISHED

        try:
            # Get initial CPU time of xwb64
            proc = next((p for p in psutil.process_iter(['name', 'cpu_times']) if p.info['name'] == self.process_name), None)
            if not proc:
                raise Exception("Process not found")
            cpu = proc.cpu_times().user + proc.cpu_times().system
            self.log(LogLevel.INFO, f"X-Ways is using {cpu} CPU.")
            self.cpu_history.append(cpu)
        except Exception as _e:
            self.log(LogLevel.ERROR, f"Error when checking X-Ways CPU activity: {traceback.format_exc()!s}")
            return XwaysProcessStatus.FINISHED
        
        oldest = self.cpu_history.get_oldest()
        if oldest is not None:
            time_oldest, Value = oldest
            if datetime.now() - time_oldest > timedelta(minutes=3):
                tolerance = 0.1
                cpu_history = self.cpu_history.get_values()
                if max(cpu_history) - min(cpu_history) < tolerance:
                    return XwaysProcessStatus.FROZEN
        
        return XwaysProcessStatus.RUNNING

    def get_exit_code(self) -> int | None:
        if self.xways_process is None:
            msg = "self.xways_process is None, which likely means that start() has not been called before."
            raise RuntimeError(msg)
        # note: "A None value indicates that the process hadn't yet terminated at the time of the last method call."
        return self.xways_process.returncode
