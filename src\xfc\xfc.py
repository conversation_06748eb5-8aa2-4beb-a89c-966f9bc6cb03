from .blocks.index import Index
from .header import <PERSON><PERSON>Header
from .evidence_item_path import XFCEvidenceItemPath
from .blocks import FileBlock

class XFC(FileBlock):
    def __init__(self, data: bytes | None = None) -> None:
        header = XFCHeader()
        path = XFCEvidenceItemPath()
        super().__init__(
            [
                header,
                path,
            ],
            Index(0),
            data,
        )
        self.header = header
        self.path = path
