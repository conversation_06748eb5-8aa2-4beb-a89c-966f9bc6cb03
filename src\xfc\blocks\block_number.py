from .block_basic import BasicBlock
from .index import Index
from enum import Enum
from .replacer import BlockReplacer

class ByteOrder(Enum):
    LITTE_ENDIAN = "little"
    BIG_ENDIAN = "big"

class IntNumberBlock(BasicBlock):
    def __init__(self, size: int, byte_order: ByteOrder = ByteOrder.LITTE_ENDIAN, is_signed: bool=False, start_at: Index = Index(0)):
        super().__init__(size=size, start=start_at)
        self.byte_order = byte_order
        self.is_signed = is_signed
    
    def get_value(self) -> int:
        return int.from_bytes(self.get_data_block(), byteorder=self.byte_order.value, signed=self.is_signed)

    def change_value(self, value: int) -> list[BlockReplacer]:
        return self.replace(value.to_bytes(2, self.byte_order.value, signed=self.is_signed))
     