from pathlib import Path

from criticalrequest import Re<PERSON><PERSON><PERSON><PERSON>, crit_request_async
from pydantic import BaseModel

from src.investigator.benchmark import BenchmarkData
from src.investigator.configuration.workflow_configuration import WorkflowConfiguration
from src.investigator.log.loglevels import LogLevel
from src.investigator.log.monitoring import Monitoring
from src.investigator.workflow.workflow_process_status import WorkflowProcessStatus

from .investigator_status import InvestigatorStatus


class ResultData(BaseModel):
    xways_project_path: Path | None
    benchmark: BenchmarkData | None

class MsgPayload(BaseModel):
    status: WorkflowProcessStatus
    step_status: InvestigatorStatus
    result_data: ResultData

class WorkflowProcessUpdater(Monitoring):
    def __init__(self, workflow_config: WorkflowConfiguration) -> None:
        super().__init__()
        self.workflow_config = workflow_config

    def update_investigator_state(
        self,
        process_id: int,
        step_status: InvestigatorStatus,
    ) -> None:
        return self.__update_workflow_process__(
            process_id=process_id,
            status=WorkflowProcessStatus.RUNNING,
            step_status=step_status,
        )

    def successfully_end_workflow_process(
            self,
            process_id: int,
            xways_project_path: Path,
            benchmark: BenchmarkData,
        ) -> None:
        return self.__update_workflow_process__(
            process_id=process_id,
            status=WorkflowProcessStatus.FINISHED,
            step_status=InvestigatorStatus.IDLE,
            xways_project_path=xways_project_path,
            benchmark=benchmark,
        )

    def fail_workflow_process(
            self,
            process_id: int,
        ) -> None:
        return self.__update_workflow_process__(
            process_id=process_id,
            status=WorkflowProcessStatus.FAILED,
            step_status=InvestigatorStatus.IDLE,
        )

    def __update_workflow_process__(
            self,
            process_id: int,
            status: WorkflowProcessStatus,
            step_status: InvestigatorStatus,
            xways_project_path: Path | None = None,
            benchmark: BenchmarkData | None = None,
        ) -> None:
        """Send the process status to the workflow API."""
        result_data = ResultData(
            xways_project_path=xways_project_path,
            benchmark=benchmark,
        )
        payload = MsgPayload(
            status=status,
            step_status=step_status,
            result_data=result_data,
        )

        url = f"{self.workflow_config.api_url}/processes/{process_id}"
        self.log(LogLevel.INFO, f"Sending data {payload} to {url}.")
        def async_logging(msg: str) -> None:
            self.log(LogLevel.INFO, message=msg)
        # Send PATCH request
        thread_processing = crit_request_async(
            RequestMethod.PATCH,
            url,
            data=payload.model_dump_json(),
            headers={"Content-Type": "application/json"},
            log_func=async_logging,
        )
        thread_processing.join()
