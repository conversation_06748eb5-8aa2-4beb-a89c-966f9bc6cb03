from .block_basic import BasicBlock
from .index import Index
from .replacer import BlockReplacer


class StrBlock(BasicBlock):
    def __init__(self, 
                 encoding: str,
                 size: int = -1
        ):
        super().__init__(size=size)
        self.encoding = encoding

    def process(self, data, start_at: Index = Index(0), end_at: Index = Index(-1)):
        if self._size == -1:
            self.set_start(start_at)
            self.set_size(end_at - start_at)
        return super().process(data, start_at, end_at)

    def get_encoding(self) -> str:
        return self.encoding
    
    def get_value(self) -> str:
        data_block = self.get_data_block()
        return data_block.decode(self.get_encoding())

    def change_value(self, value: str) -> list[BlockReplacer]:
        return self.replace(value.encode(self.get_encoding()))