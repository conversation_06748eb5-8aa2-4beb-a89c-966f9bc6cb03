from __future__ import annotations
from .i_block import IBlock, RealtionInfo
from .index import Index, ChildIndex
from .replacer import BlockReplacer

class BasicBlock(IBlock):
    def __init__(
            self, 
            data: bytes | None = None, 
            size: int = -1, 
            sub_blocks: list[IBlock] | None = None, 
            start: Index = Index(0)
        )-> None:
        self._size = size
        self._sub_blocks = []
        if sub_blocks is not None:
            self._sub_blocks = sub_blocks
            for idx, block in enumerate(self._sub_blocks):
                block.set_parent_info(RealtionInfo(self,ChildIndex(idx)))
        self._start = start
        self._parent_info: RealtionInfo | None = None
        self._data = data

    def set_parent_info(self, parent_info: RealtionInfo) -> None:
        self._parent_info = parent_info

    def get_parent_info(self) -> RealtionInfo | None:
        return self._parent_info
    
    def process(self, data: bytes, start_at: Index = Index(0), end_at: Index = Index(-1)) -> None:
        self._data = data
        start = start_at
        for child in  self.get_children():
            child.process(data, start, end_at)
            start = child.get_end()
        if len(self.get_children()) != 0:
            sub_blocks = self.get_children()
            self.set_start(sub_blocks[0].get_start())
            self.set_size(self.get_children()[-1].get_end()-self.get_start())
        elif self.get_size() > 0 and self.get_start() == Index(0):
            self.set_start(start_at)
        call_stack = self.get_call_stack()
        identifier = " -> ".join(call_stack)
        print(f"{identifier}: {hex(self.get_start())}->{hex(self.get_end())}") # ({self.get_size()}):" + self.get_data_block().hex())

    def get_data(self) -> bytes:
        if self._data is None:
            msg = "Please setup the value before using this function!"
            raise ValueError(msg)
        return self._data

    def set_start(self, start: Index) -> None:
        self._start = start

    def get_start(self) -> Index:
        return self._start
    
    def set_size(self, size: int) -> None:
        self._size = size

    def get_size(self) -> int:
        return self._size

    def get_end(self) -> Index:
        return Index(self._start + Index(self._size))
    
    def get_children(self) -> list[IBlock]:
        return self._sub_blocks
    
    def match(self,data: bytes) -> bool:
        for child in self.get_children():
            if not child.match(data):
                return False
        return True
    
    def generate_new_data(self, replacers: list[BlockReplacer]) -> bytes:
        replacers_best_processed = sorted(replacers, key=lambda r: r.original.get_start(), reverse=True)
        data = bytes(self.get_data())
        for replacer in replacers_best_processed:
            data = replacer.replace(data)
        return data
    
    def coordinate_replacement(
            self, 
            child_index: ChildIndex, 
            replacers: list[BlockReplacer],
        ) -> list[BlockReplacer]:
        info = self.get_parent_info()
        if info is not None:
            return info.parent.coordinate_replacement(info.child_index, replacers)
        return replacers
            

    def replace(self, data: bytes) -> list[BlockReplacer]:
        replacers = [BlockReplacer(self,  data)]
        info = self.get_parent_info()
        if info is not None:
            return info.parent.coordinate_replacement(info.child_index, replacers)
        return replacers
        
    def get_call_stack(self) -> list[str]:
        info = self.get_parent_info()
        call_path = list[str]()
        while info is not None:
            call_path.append(
                f"{info.parent.__class__.__name__}: {info.child_index}"
            )
            info = info.parent.get_parent_info()
        if len(call_path) != 0:
            call_path.reverse()
        call_path.append(f"({self.__class__.__name__})")
        return call_path