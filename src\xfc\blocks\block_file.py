from pathlib import Path
from .block_basic import BasicBlock, IBlock
from .index import Index


class FileBlock(BasicBlock):
    def __init__(self, sub_blocks: list[IBlock], start: Index = Index(0), data: bytes | None = None) -> None:
        super().__init__(
            data=data, 
            sub_blocks=sub_blocks, 
            start=start
        )

    def load_from_file(self, file: Path)->None:
        with file.open('rb') as f:
            self._data = f.read()
        self.process(self.get_data())
    
    def save_data(self, file: Path) -> None:
        if self.get_data() is None:
            return None
        with file.open('wb+') as f:
            f.write(self.get_data())
