from pydantic import BaseModel


class RAMInfo(BaseModel):
    size_bytes: int
    speed_read_bytes_s: float
    speed_write_bytes_s: float

    @classmethod
    def measure(cls) -> "RAMInfo":
        import psutil
        import numpy as np
        import time

        size = 100_000_000  # ~100MB
        arr = np.random.rand(size)

        start = time.time()
        _ = arr.copy()  # Simulate read
        read_time = time.time() - start

        start = time.time()
        arr[:] = np.random.rand(size)  # Simulate write
        write_time = time.time() - start
        return cls(
            size_bytes = psutil.virtual_memory().total,
            speed_read_bytes_s = size / read_time,
            speed_write_bytes_s = size / write_time,
        )

