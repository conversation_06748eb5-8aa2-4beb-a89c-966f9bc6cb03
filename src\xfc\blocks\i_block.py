from __future__ import annotations
from abc import abstractmethod
from dataclasses import dataclass
from .index import Index, ChildIndex
from .i_marker import IMarker
from .replacer import BlockReplacer

@dataclass(frozen=True)
class RealtionInfo:
    parent: IBlock
    child_index: ChildIndex


class IBlock(IMarker):
    @abstractmethod
    def set_parent_info(self, parent_info: RealtionInfo) -> None:
        pass

    @abstractmethod
    def get_parent_info(self) -> RealtionInfo | None:
        pass

    @abstractmethod
    def set_start(self, start: Index) -> None:
        pass
    
    @abstractmethod
    def process(self, data: bytes, start_at: Index = Index(0), end_at: Index = Index(-1)) -> None:
        pass

    @abstractmethod
    def get_data(self) -> bytes:
        pass

    def get_data_block(self) -> bytes:
        return self.get_data()[self.get_start():self.get_end()]

    @abstractmethod
    def set_size(self, size: int) -> None:
        pass

    @abstractmethod
    def match(self,data: bytes) -> bool:
        pass

    @abstractmethod
    def get_children(self) -> list[IBlock]:
        pass

    @abstractmethod
    def coordinate_replacement(
        self, 
        child_index: ChildIndex, 
        replacers: list[BlockReplacer],
    ) -> list[BlockReplacer]:
        pass