import re
from .block_basic import BasicBlock
from .index import Index

class ZeroBufferBlock(BasicBlock):
    def __init__(self, start: Index = Index(0),  offset_end: Index = Index(0)):
        super().__init__(start=start)
        self.offset_end = offset_end

    def process(self, data, start_at: Index = Index(0), end_at: Index = Index(-1)):
        regex_match = re.search(br'\x00+', data[start_at:end_at])
        if not regex_match:
            msg = "Not found!"
            raise ValueError(msg)
        self.set_start(Index(Index(regex_match.start())+ start_at))
        end_index = Index(regex_match.end())
        self.set_size(end_index + self.offset_end)
        return super().process(data, start_at, end_at)
    
    def change_size(self, delta_size):
        new_size = self.get_size() + delta_size
        if new_size < 0:
            msg = "Size can not be negative!"
            raise ValueError(msg) 
        return self.replace(bytes([0x00]*new_size))