from pydantic import BaseModel, PrivateAttr
import psutil


def get_process_by_name(name) -> psutil.Process|None:
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] == name:
            return proc
    return None

class ResourceUsage(BaseModel):
    cpu_cores_max: int
    cpu_usage_percent_max: float
    memory_usage_bytes_max: int

    def __init__(self) -> None:
        super().__init__(
            cpu_cores_max = 0,
            cpu_usage_percent_max = 0.0,
            memory_usage_bytes_max = 0
        )

    def update(self, process: psutil.Process) -> None:
        self.cpu_cores_max = max(
            self.cpu_cores_max,
            len(process.cpu_affinity())
        )
        self.cpu_usage_percent_max = max(
            self.cpu_usage_percent_max, 
            process.cpu_percent(interval=1)
        )
        self.memory_usage_bytes_max = max(
            self.memory_usage_bytes_max,
            process.memory_info().rss
        )

