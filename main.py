import json
import logging
import os
import sys
import time
import traceback
from pathlib import Path

import click

from src.investigator.configuration.configuration import Configuration
from src.investigator.investigator_prerequisites import InvestiGatorPrerequisiteCheck
from src.investigator.log.setup_logging import setup_logging
from src.investigator.workflow.next_job_requester import Next<PERSON>obRequester
from src.investigator.xways_rvs.next_job_runner import <PERSON><PERSON>obRunner

@click.group()
@click.pass_context
def investigator(ctx: click.Context) -> None:
    setup_logging()
    logger = logging.getLogger(os.path.basename(__file__))
    logger.info("Running InvestiGator ...")

    try:
        config_path = Path.cwd() / "investigator_config.json"
        # Read JSON file
        with config_path.open("r") as file:
            data = json.load(file)
        # Validate data against Pydantic model
        ctx.obj = (logger, Configuration(**data))
    except Exception as _e:
        logger.error(f"Error reading config file: {traceback.format_exc()}")
        sys.exit(1)


@investigator.command("run-next-job")
@click.pass_context
def run_once(ctx: click.Context) -> None:
    """
    Run the next investigator job, if one exits.
    """
    context: tuple[logging.Logger, Configuration] = ctx.obj
    logger, config = context

    # check if prerequisites are fulfilled (e.g. NAS reachable)
    if not InvestiGatorPrerequisiteCheck(config.workflow, config.next_job).are_all_prerequisites_fulfilled():
        logger.error("Prerequisites not fulfilled. I quit.")
        sys.exit(1)

    next_job_runner = NextJobRunner(config)
    next_job = NextJobRequester(config.workflow, config.next_job).reserve_next_job()
    if next_job is not None:
        if not next_job_runner.run(next_job):
            sys.exit(1)
    else:
        logger.info(f"There is no next job for me to run.")

    sys.exit(0)


@investigator.command("run-forever")
@click.pass_context
def run_forever(ctx: click.Context):
    context: tuple[logging.Logger, Configuration] = ctx.obj
    logger, config = context
    job_requester = NextJobRequester(config.workflow, config.next_job)
    next_job_runner = NextJobRunner(config)

    while True:

        if not InvestiGatorPrerequisiteCheck(config.workflow, config.next_job).are_all_prerequisites_fulfilled():
            logger.error("Prerequisites not fulfilled. I will try again in 2 minutes.")
            time.sleep(120)
            continue

        next_job = job_requester.reserve_next_job()
        if next_job is None:
            time.sleep(10)
            continue
        next_job_runner.run(next_job)

if __name__ == '__main__':
    investigator()