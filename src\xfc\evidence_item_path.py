from pathlib import Path
from typing import cast

from .blocks.index import Index
from .blocks import (
    BasicBlock,
    MagicBlock,
    IntNumberBlock,
    StrBlock,
    BlockReplacer,
)

import re


class XFCEvidenceItemPath(BasicBlock):
    def __init__(self) -> None:
        # Define the pattern
        self.pattern = re.compile(
            rb'(?P<size>.{2})'                    # Capture size (2 bytes) -> START SIZE
            rb'(?P<magic>\x00\x00[^\x00]\x5B\x00)'# Magic sequence with[
            rb'(?P<path>(?:[^\x00]\x00)+)'        # Capture path (2 bytes per char, non-greedy)
            rb'(?P<buffer>\x00*?)'                # Capture zero buffer --> END SIZE
            rb'\x00\x00\x03'                      # End marker
        )
        super().__init__()

    def process(self, data, start_at = Index(0), end_at = Index(-1)):
        self._sub_blocks.clear()
        findings = self.pattern.finditer(data[start_at:end_at])
        for finding in findings:
            size = finding.group("size")
            magic = finding.group("magic")
            path_raw = finding.group("path")
            buffer = finding.group("buffer")
            start, end = finding.span()
            self._sub_blocks.append(
                BasicBlock(
                    start=Index(start+start_at), size=Index(end-start),
                    sub_blocks=[
                        IntNumberBlock(start_at=Index(start+start_at), size=len(size)),
                        MagicBlock(magic),
                        StrBlock("utf-16le", size=len(path_raw)),
                        BasicBlock(size=len(buffer))
                    ]
                )    
            )
        return super().process(data, start_at, end_at)

    def get_path(self) -> Path:
        block = self.get_children()[0]
        path = cast(StrBlock, block.get_children()[3])
        return Path(path.get_value())
    
    def change_path(self, path: Path) -> list[BlockReplacer]:
        path_raw = str(path).encode("utf-16le")
        changes = list[BlockReplacer]()
        for child in self.get_children():
            block_size = cast(IntNumberBlock, child.get_children()[0])
            block_path = cast(StrBlock, child.get_children()[-2])
            block_buffer = cast(StrBlock, child.get_children()[-1])
            data = block_path.get_data_block()
            index = data.find(b'\x5D\x00')
            change_path = block_path.replace(path_raw + data[index:])[0]
            changes.append(change_path)
            if change_path.get_size_diff() == 0:
                continue
            if change_path.get_size_diff() < block_buffer.get_size():
                new_length = block_buffer.get_size() + change_path.get_size_diff()
                changes.append(block_buffer.replace(bytes([0x00] * new_length))[0])
            else:
                new_size = block_size.get_value() + change_path.get_size_diff()
                changes.append(block_size.change_value(new_size)[0])
        return BlockReplacer.simplify(changes)
