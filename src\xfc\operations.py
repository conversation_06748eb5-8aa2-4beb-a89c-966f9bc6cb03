from .blocks import (
    BasicBlock,
    StrBlock,
    MagicBlock,
    EmbeddedBlock,
)


class XFCOperations(BasicBlock):
    def __init__(self) -> None:
        success = StrBlock("latin-1")
        operation = StrBlock("latin-1")
        log = StrBlock("latin-1")
        # partitions = StrBlock(16, encoding="latin-1")
        operation2 = StrBlock("latin-1")

        super().__init__(sub_blocks=[
            MagicBlock(bytes([
                0x0D, 0x00, 0x08, 0x00, 0x00, 0x00, 
            ])),
            BasicBlock(size=23),
            EmbeddedBlock(
                [success], 
                end_block= MagicBlock(bytes([0x00])),
            ),
            BasicBlock(size=8),
            MagicBlock(bytes([0x00])),
            EmbeddedBlock(
                [operation], 
                end_block= MagicBlock(bytes([0xF0, 0xDB, 0x01, 0x06])),
            ),
            EmbeddedBlock(
                [
                    log,
                    # BasicBlock(5),
                ],
                end_block= MagicBlock(bytes([0xF0, 0xDB, 0x01, 0x06,])),
            ),
            # EmbeddedBlock(
            #     [
            #         partitions,
            #     ],
            #     end_block = MagicBlock(bytes([0xF0, 0xDB, 0x01, 0x00,])),
            # ),
            EmbeddedBlock(
                [
                    operation2
                ],
                end_block = MagicBlock(bytes([0x00, 0x17, 0x00, 0x12,])),
            )
        ])
        self.success = success
        self.operation = operation
        self.log = log
        # self.partitions = partitions
        self.operation2 = operation2