# InvestiGator

## WTF?
The InvestiGator is an automation for running RVS (Refine Volume Snapshot, dt. "Datei-Überblick erweitern") in X-Ways. In summary, it 
1. requests its next job from the [ITF workflow](https://gitea.digifors.local/IT-Forensik/IT-Forensic_Workflow_Automation) API,
2. creates an X-Ways project and adds the image specified in the next job response,
3. runs the RVS process in X-Ways


## Installation
Installation is described at this [wiki page](https://wiki.digifors.de/link/1621#bkmrk--2).

## ToDos
major or urgent:
- [X] work with a local xways project file (create the xways project locally and copy to NAS after RVS finished to not be slowed down by network speed)
- [X] save a backup of the original xfc file (and bak files) before replacing the evidence item path
- [X] save xways settings file (WinHex.cfg) in gitea
- [ ] check a few more indicators to determine a successful Xways RVS
- [ ] try again when frozen: if the RVS is frozen, quit X-Ways, open it again and try to continue the RVS (in a lot of cases this can solve the issue)

minor or not urgent:
- [ ] add a bit more details about how the RVS Station is working in this readme
- [ ] Support multiple images root folders (requires ITF workflow software sending which images root folder to use in the result_data of the next job)
- [ ] next_job_requester.py: use pydantic model to validate next-job response from the workflow software, same for `input_data` in the next-job workflow response and `first_image_file_path` in input_data (maybe the ITF workflow software switched to a nested response instead of dumping json into `input_data` in the meantime)
- [ ] workflow_process_updater.py: use pydantic model to dump process and send process update to the workflow (recommended: use the same pydantic model in investigator and workflow) instead of constructing the payload
