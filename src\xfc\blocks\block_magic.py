from .block_basic import BasicBlock
from .index import Index


class MagicBlock(BasicBlock):
    def __init__(self, sequence: bytes):
        super().__init__(size=len(sequence))
        self.sequence = sequence
    
    def process(self, data, start_at: Index = Index(0), end_at: Index = Index(-1)):
        index = Index(data[start_at:end_at].find(self.sequence))
        if index == -1:
            call_stack = self.get_call_stack()
            identifier = " -> ".join(call_stack)
            msg = f"{identifier}: magic sequence not found. ({self.sequence.hex()})"
            raise ValueError(msg)
        self.set_start(Index(index + start_at))
        super().process(data, start_at, end_at)
        print(f"\t{self.get_data_block().hex()}")